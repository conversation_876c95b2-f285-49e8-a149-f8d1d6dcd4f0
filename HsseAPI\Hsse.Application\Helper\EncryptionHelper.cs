﻿using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Application.Helper
{
    public class EncryptionHelper
    {
        private readonly IConfiguration _configuration;

        public EncryptionHelper(IConfiguration configuration)
        {
            _configuration = configuration;
        }
        
        public string Encrypt(string plainText)
        {
            var encriptionSettings = _configuration.GetSection("EncryptionSettings");
            using var aes = Aes.Create();
            aes.Key = Encoding.UTF8.GetBytes(encriptionSettings["Key"]);
            aes.IV = Encoding.UTF8.GetBytes(encriptionSettings["IV"]);

            using var encryptor = aes.CreateEncryptor();
            var plainBytes = Encoding.UTF8.GetBytes(plainText);
            var encryptedBytes = encryptor.TransformFinalBlock(plainBytes, 0, plainBytes.Length);

            return Convert.ToBase64String(encryptedBytes);
        }

        public string Decrypt(string encryptedText)
        {
            var encriptionSettings = _configuration.GetSection("EncryptionSettings");
            using var aes = Aes.Create();
            aes.Key = Encoding.UTF8.GetBytes(encriptionSettings["Key"]);
            aes.IV = Encoding.UTF8.GetBytes(encriptionSettings["IV"]);

            using var decryptor = aes.CreateDecryptor();
            var encryptedBytes = Convert.FromBase64String(encryptedText);
            var decryptedBytes = decryptor.TransformFinalBlock(encryptedBytes, 0, encryptedBytes.Length);

            return Encoding.UTF8.GetString(decryptedBytes);
        }
    }

}
